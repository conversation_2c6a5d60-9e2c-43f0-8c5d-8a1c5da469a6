import pandas as pd
import time
import sys
import os
import traceback


def save_console_output(file_path, func, *args, **kwargs):
    with open(file_path, 'w') as f:
        try:
            # Pass the file handle to the function
            func(f, *args, **kwargs)
        except Exception as e:
            error_msg = f"Error: {str(e)}"
            print(error_msg)  # Console
            print(error_msg, file=f)  # File
            traceback.print_exc()  # Console
            traceback.print_exc(file=f)  # File


def process_mtl_file(mtl_path, timestep, master_csv_path):
    """
    Reads a .mtl-like file, adds a timestep column, and appends to a master CSV file.

    Args:
        mtl_path (str): Path to the .mtl file.
        timestep (int or float): Timestep value to add.
        master_csv_path (str): Path to the master CSV file to append to.
    """
    # Read the space-separated values into a DataFrame
    df = pd.read_csv(mtl_path, sep='\s+', header=None, names=['Index', 'Group', 'Value1', 'Value2'])

    # Add the Timestep column
    df['Timestep'] = timestep

    # Check if master CSV exists
    if os.path.exists(master_csv_path):
        # Append to existing CSV
        df.to_csv(master_csv_path, mode='a', header=False, index=False)
    else:
        # Create new CSV with headers
        df.to_csv(master_csv_path, index=False)

    print(f"Appended data from {mtl_path} (Timestep {timestep}) to {master_csv_path}")


def main(output_file=None):
    def dual_print(message):
        print(message)  # Console
        if output_file:
            print(message, file=output_file)
            output_file.flush()

    dual_print("Main function placeholder")

    Dig_path = r"D:\\Program Files\\DIgSILENT\\PowerFactory 2024 SP1\\Python\\3.12"
    sys.path.append(Dig_path)
    os.environ['PATH'] += ';' + Dig_path

    import powerfactory as pf
    app = pf.GetApplication()
    if app is None:
        raise Exception('getting Powerfactory application failed')

    # define project name and study case
    projName = 'Nine-bus System(1)_testDynamic_load'
    study_case = '08- Three Cycles IEEE Type 1 Mag-A_PSS'

    # activate project
    project = app.ActivateProject(projName)
    proj = app.GetActiveProject()

    # get the study case folder and activate project
    oFolder_studycase = app.GetProjectFolder('study')
    oCase = oFolder_studycase.GetContents(study_case)[0]
    oCase.Activate()

    sim = app.GetFromStudyCase('ComSim')

    sim.iopt_sim = 1
    sim.tstart = 0.0
    sim.tstop = 100.0
    sim.dtgrd = 1e-5

    buses = app.GetCalcRelevantObjects('*.ElmTerm')
    lines = app.GetCalcRelevantObjects('*.ElmLne')
    generators = app.GetCalcRelevantObjects('*.ElmSym')

    results = app.GetFromStudyCase('*.ElmRes')
    if results is None:
        results = app.CreateObject('ElmRes', 'Results')
        results.SetAttribute('loc_name', 'VoltageResults')

    results.Clear()

    num_samples = 5
    sampling_interval = sim.tstop / num_samples
    data = pd.DataFrame()

    for i in range(num_samples):
        sample_time = i * sampling_interval
        sim.tstop = sample_time
        sim.Execute()

        row = {'Time': sample_time}
        for generator in generators:
            gP = generator.GetAttribute('s:P1')
            gQ = generator.GetAttribute('s:Q1')
            speed = generator.GetAttribute('s:speed')
            row[f'{generator.loc_name} P'] = gP
            row[f'{generator.loc_name} Q'] = gQ
            row[f'{generator.loc_name} speed'] = speed

        for bus in buses:
            vm = bus.GetAttribute('m:u1')
            va = bus.GetAttribute('m:phiu')
            row[f'{bus.loc_name} Vm'] = vm
            row[f'{bus.loc_name} V_angle'] = va

        for line in lines:
            Im = line.GetAttribute('m:I1:bus1')
            Ia = line.GetAttribute('m:phii:bus1')
            row[f'{line.loc_name} Im'] = Im
            row[f'{line.loc_name} I_angle'] = Ia

        modal = app.GetFromStudyCase('ComMod')
        if modal is None:
            dual_print("Error: Could not get modal analysis command")
            continue

        try:
            modal.iopt_mod = 1
            modal.iopt_ev = 1
            modal.iopt_evsel = 0
            modal.iopt_evsort = 1
            modal.iopt_partfact = 1
            modal.iopt_evec = 1
            modal.iopt_met = 0
            modal.iopt_noevec = 0
            modal.iopt_which = 0

            dual_print("\nModal analysis configuration:")
            dual_print(f"iopt_mod: {modal.iopt_mod}")
            dual_print(f"iopt_ev: {modal.iopt_ev}")
            dual_print(f"iopt_evsel: {modal.iopt_evsel}")
            dual_print(f"iopt_evsort: {modal.iopt_evsort}")
            dual_print(f"iopt_partfact: {modal.iopt_partfact}")
            dual_print(f"iopt_evec: {modal.iopt_evec}")
            dual_print(f"iopt_met: {modal.iopt_met}")
            dual_print(f"iopt_noevec: {modal.iopt_noevec}")
            dual_print(f"iopt_which: {modal.iopt_which}")

            dual_print(f"\nPerforming modal analysis at time {sample_time:.4f} s...")
            result = modal.Execute()
            dual_print(f"Modal analysis execution result: {result}")

            mtl_path = r'C:\Users\<USER>\Downloads\modal_res\EVals.mtl'
            eigenvalues = pd.read_csv(mtl_path, sep=r'\s+', header=None, names=['Index', 'Group', 'Value1', 'Value2'])
            eigenvalues['Timestep'] = sample_time

            for idx, (real, imag) in enumerate(zip(eigenvalues['Value1'], eigenvalues['Value2'])):
                row[f'EV_{idx}_real'] = real
                row[f'EV_{idx}_imag'] = imag

            data = pd.concat([data, pd.DataFrame([row])], ignore_index=True)

        except Exception as e:
            dual_print(f"Error in modal analysis at time {sample_time:.4f} s: {str(e)}")

        if i < num_samples - 1:
            sim.tstop = 0.0
            sim.Execute()
            time.sleep(0.5)

    data.to_csv(f"modal_analysis_data_cut5_{time.time()}.csv", index=False)
    dual_print(str(data))


if __name__ == "__main__":
    save_console_output(f'console_output_{time.time()}.txt', main)