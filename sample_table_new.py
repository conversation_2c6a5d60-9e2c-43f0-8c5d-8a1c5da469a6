
### this one is  to sample the data according to the number of smaple and \\
# the simulation duration to decide the sample frequency

from datetime import datetime

import pandas as pd
import time
import sys
import os
import pandas as pd
import os

from fontTools.varLib.instancer.names import getVariationNameIDs


def process_mtl_file(mtl_path, timestep, master_csv_path):
    """
    Reads a .mtl-like file, adds a timestep column, and appends to a master CSV file.

    Args:
        mtl_path (str): Path to the .mtl file.
        timestep (int or float): Timestep value to add.
        master_csv_path (str): Path to the master CSV file to append to.
    """
    # Read the space-separated values into a DataFrame
    df = pd.read_csv(mtl_path, sep='\s+', header=None, names=['Index', 'Group', 'Value1', 'Value2'])

    # Add the Timestep column
    df['Timestep'] = timestep

    # Check if master CSV exists
    if os.path.exists(master_csv_path):
        # Append to existing CSV
        df.to_csv(master_csv_path, mode='a', header=False, index=False)
    else:
        # Create new CSV with headers
        df.to_csv(master_csv_path, index=False)

    print(f"Appended data from {mtl_path} (Timestep {timestep}) to {master_csv_path}")


# Example usage

######################

Dig_path = r"D:\\Program Files\\DIgSILENT\\PowerFactory 2024\\Python\\3.12"
sys.path.append(Dig_path)
os.environ['PATH'] += ';' + Dig_path

if __name__ == "__main__":
    import powerfactory as pf

app = pf.GetApplication()
if app is None:
    raise Exception('getting Powerfactory application failed')

# define project name and study case
projName = 'Nine-bus System'  # 'Nine-bus System'  Nine-bus System(1)_testDynamic_load



study_case = '08- Three Cycles IEEE Type 1 Mag-A_PSS'  # dont change this name  08- Three Cycles IEEE Type 1 Mag-A_PSS   03- Three Cycles Standard SG Model

# activate project
project = app.ActivateProject(projName)
proj = app.GetActiveProject()

# get the study case folder and activate project
oFolder_studycase = app.GetProjectFolder('study')  # dont change this folder name

oCase = oFolder_studycase.GetContents(study_case)[0]
oCase.Activate()
#
# grid_var = project.GetObject('Network Variants/IEEE Type 1 Mag-A')
# if grid_var:
#     grid_var.Activate()
#     print(f"Activated variation: {grid_var.loc_name}")
# else:
#     print("Variation not found.")

# Get EMT simulation command
sim = app.GetFromStudyCase('ComSim')  # Or use ComEMT if available

# Set simulation parameters
sim.iopt_sim = 1  # 1 = EMT simulation
sim.tstart = 0.0
sim.tstop = 100.0  # Run to 1 second
sim.dtgrd = 1e-5  # EMT timestep

# Get result bus
buses = app.GetCalcRelevantObjects('*.ElmTerm')  #
lines = app.GetCalcRelevantObjects('*.ElmLne')  #
generators = app.GetCalcRelevantObjects('*.ElmSym')  #

# Create a results object to store snapshots
results = app.GetFromStudyCase('*.ElmRes')
if results is None:
    results = app.CreateObject('ElmRes', 'Results')
    results.SetAttribute('loc_name', 'VoltageResults')

# Clear any existing results
results.Clear()

# Sampling setup
num_samples = 5  # Number of samples to take
sampling_interval = sim.tstop / num_samples  # Time between samples
data = pd.DataFrame()
modal_results = []

# Run EMT simulation and sample at different time points
print(f"Starting EMT simulation from {sim.tstart} to {sim.tstop} seconds...")
print(f"Will sample at {num_samples} time points with interval {sampling_interval:.2f} seconds")

for i in range(num_samples):
    sample_time = i * sampling_interval

    print(f"\n{'='*60}")
    print(f"SAMPLE {i+1}/{num_samples} AT TIME {sample_time:.4f} SECONDS")
    print(f"{'='*60}")

    # Run EMT simulation up to this sample time
    sim.tstop = sample_time
    print(f"Running EMT simulation to time {sample_time:.4f} s...")
    sim_result = sim.Execute()
    print(f"EMT simulation result: {sim_result}")

    # Collect current EMT simulation values
    print(f"Collecting EMT simulation values at time {sample_time:.4f} s...")
    row = {'Time': sample_time}

    # Generator values
    print("Generator states:")
    for generator in generators:
        gP = generator.GetAttribute('s:P1')
        gQ = generator.GetAttribute('s:Q1')
        speed = generator.GetAttribute('s:speed')
        row[f'{generator.loc_name} P'] = gP
        row[f'{generator.loc_name} Q'] = gQ
        row[f'{generator.loc_name} speed'] = speed
        print(f"  {generator.loc_name}: P={gP:.3f} MW, Q={gQ:.3f} MVAr, Speed={speed:.6f} pu")

    # Bus voltages
    print("Bus voltages:")
    for bus in buses:
        vm = bus.GetAttribute('m:u1')
        va = bus.GetAttribute('m:phiu')
        row[f'{bus.loc_name} Vm'] = vm
        row[f'{bus.loc_name} V_angle'] = va
        print(f"  {bus.loc_name}: V={vm:.3f} pu, Angle={va:.2f} deg")

    # Line currents
    for line in lines:
        Im = line.GetAttribute('m:I1:bus1')
        Ia = line.GetAttribute('m:phii:bus1')
        row[f'{line.loc_name} Im'] = Im
        row[f'{line.loc_name} I_angle'] = Ia
    # Get modal analysis command
    modal = app.GetFromStudyCase('ComMod')
    if modal is None:
        print("Error: Could not get modal analysis command")
        continue

    try:
        print(f"\n=== MODAL ANALYSIS AT TIME {sample_time:.4f} s ===")

        # STEP 1: Capture current EMT state by performing a load flow with current values
        print("Step 1: Capturing current EMT state...")

        # Get load flow command
        ldf = app.GetFromStudyCase('ComLdf')
        if ldf is None:
            print("Error: Could not get load flow command")
            continue

        # Configure load flow to use current simulation values as initial guess
        ldf.iopt_net = 0    # AC load flow
        ldf.iopt_at = 0     # Automatic tap adjustment off
        ldf.iopt_asht = 0   # Automatic shunt adjustment off
        ldf.iopt_plim = 0   # No active power limits
        ldf.iopt_lim = 0    # No reactive power limits

        # CRITICAL: Set all generators to use current EMT values
        print("Setting generators to current EMT values...")
        for generator in generators:
            # Get current EMT values
            current_P = generator.GetAttribute('s:P1')  # Current active power from EMT
            current_Q = generator.GetAttribute('s:Q1')  # Current reactive power from EMT
            current_speed = generator.GetAttribute('s:speed')  # Current speed from EMT

            # Set these as the operating point for load flow
            generator.SetAttribute('pgini', current_P)  # Set initial active power
            generator.SetAttribute('qgini', current_Q)  # Set initial reactive power

            print(f"  {generator.loc_name}: P={current_P:.3f} MW, Q={current_Q:.3f} MVAr, Speed={current_speed:.6f} pu")

        # Set all buses to use current EMT voltages
        print("Setting buses to current EMT voltages...")
        for bus in buses:
            current_vm = bus.GetAttribute('m:u1')    # Current voltage magnitude from EMT
            current_va = bus.GetAttribute('m:phiu')  # Current voltage angle from EMT

            # Set these as initial values for load flow
            bus.SetAttribute('u0', current_vm)       # Set initial voltage magnitude
            bus.SetAttribute('phiu0', current_va)    # Set initial voltage angle

            print(f"  {bus.loc_name}: V={current_vm:.3f} pu, Angle={current_va:.2f} deg")

        # Execute load flow to establish consistent steady-state from EMT snapshot
        print("Executing load flow with EMT snapshot values...")
        ldf_result = ldf.Execute()
        print(f"Load flow result: {ldf_result}")

        if ldf_result != 0:
            print(f"Warning: Load flow did not converge (result: {ldf_result})")
            # Continue anyway as we want to use the EMT state

        # STEP 2: Now perform modal analysis on this state
        print("\nStep 2: Performing modal analysis on captured state...")

        # Configure modal analysis
        modal.iopt_mod = 1      # Modal analysis mode
        modal.iopt_ev = 1       # Calculate eigenvalues
        modal.iopt_evsel = 0    # All eigenvalues
        modal.iopt_evsort = 1   # Sort eigenvalues
        modal.iopt_partfact = 1 # Participation factors
        modal.iopt_evec = 1     # Calculate eigenvectors
        modal.iopt_met = 0      # Method
        modal.iopt_noevec = 0   # Number of eigenvectors
        modal.iopt_which = 0    # Which eigenvalues
        modal.iopt_init = 0     # Use current state as initialization

        print("Modal analysis configuration:")
        print(f"  iopt_mod: {modal.iopt_mod}")
        print(f"  iopt_ev: {modal.iopt_ev}")
        print(f"  iopt_init: {modal.iopt_init}")

        # Execute modal analysis
        print(f"Executing modal analysis...")
        result = modal.Execute()
        print(f"Modal analysis execution result: {result}")

        mtl_path = r'C:\Users\<USER>\Downloads\modal_res\EVals.mtl'

        # Small delay to ensure file is written
        time.sleep(0.3)

        # Verify the MTL file exists and check its modification time
        if os.path.exists(mtl_path):
            file_mtime = os.path.getmtime(mtl_path)
            file_time = time.ctime(file_mtime)
            print(f"MTL file last modified: {file_time}")
        else:
            print(f"Warning: MTL file not found at {mtl_path}")
            continue

        # process_mtl_file(mtl_path, timestep=sample_time, master_csv_path='Master_EVals.csv')

        # # Parse MTL file at this time step
        # eigenvalues = []
        # with open(mtl_path, 'r') as f:
        #     for line in f:
        #         parts = line.strip().split()
        #         eigval = complex(float(parts[1]), float(parts[2]))
        #         eigenvalues.append(eigval)
        # for idx, eig in enumerate(eigenvalues):
        #     row[f'EV_{idx}_real'] = eig.real
        #     row[f'EV_{idx}_imag'] = eig.imag
        #
        # data = pd.concat([data, pd.DataFrame([row])], ignore_index=True)



        #
        # # Read the space-separated values into a DataFrame
        # eigenvalues = pd.read_csv(mtl_path, sep=r'\s+', header=None, names=['Index', 'Group', 'Value1', 'Value2'])
        #
        # # Add the Timestep column
        # eigenvalues['Timestep'] = sample_time
        #
        # # Construct a new row with real and imaginary parts of eigenvalues
        # row = {'Timestep': sample_time}
        # for idx, (real, imag) in enumerate(zip(eigenvalues['Value1'], eigenvalues['Value2'])):
        #     row[f'EV_{idx}_real'] = real
        #     row[f'EV_{idx}_imag'] = imag
        #
        # # Convert to a DataFrame and append to the master CSV
        # new_row_df = pd.DataFrame([row])
        #
        #
        # data = pd.concat([data, pd.DataFrame([row])], ignore_index=True)






        # Read the space-separated values into a DataFrame
        eigenvalues = pd.read_csv(mtl_path, sep=r'\s+', header=None, names=['Index', 'Group', 'Value1', 'Value2'])

        # Add the Timestep column (optional if already in row)
        eigenvalues['Timestep'] = sample_time

        # Debug: Print first few eigenvalues to verify they're changing
        print(f"First 3 eigenvalues at time {sample_time:.4f}:")
        for idx in range(min(3, len(eigenvalues))):
            real_part = eigenvalues.iloc[idx]['Value1']
            imag_part = eigenvalues.iloc[idx]['Value2']
            print(f"  EV_{idx}: {real_part:.6f} + {imag_part:.6f}j")

        # Add eigenvalues to the existing row dictionary
        for idx, (real, imag) in enumerate(zip(eigenvalues['Value1'], eigenvalues['Value2'])):
            row[f'EV_{idx}_real'] = real
            row[f'EV_{idx}_imag'] = imag

        print(f"Successfully processed {len(eigenvalues)} eigenvalues for time {sample_time:.4f} s")

        # Append to the main data DataFrame
        data = pd.concat([data, pd.DataFrame([row])], ignore_index=True)







    except Exception as e:
        print(f"Error in modal analysis at time {sample_time:.4f} s: {str(e)}")

    # If this isn't the last sample, prepare for the next run
    if i < num_samples - 1:
        next_sample_time = (i+1) * sampling_interval
        print(f"\nPreparing for next sample at time {next_sample_time:.4f} s...")
        print("Continuing EMT simulation from current state (no reset)...")

        # Brief pause for system stability
        time.sleep(0.1)
data.to_csv(
    f"modal_analysis data_cut5_{datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}.csv",
    index=False)
print(data)
