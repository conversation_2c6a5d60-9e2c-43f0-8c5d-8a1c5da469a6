import time
import sys
import os
import pandas as pd
import os

# Example usage

######################

Dig_path = r"D:\\Program Files\\DIgSILENT\\PowerFactory 2024\\Python\\3.12"
sys.path.append(Dig_path)
os.environ['PATH'] += ';' + Dig_path


if __name__ == "__main__":
    import powerfactory as pf

app = pf.GetApplication()
if app is None:
    raise Exception('getting Powerfactory application failed')

# define project name and study case
projName = 'Nine-bus System(1)_testDynamic_load'  # 'Nine-bus System'  Nine-bus System(1)_testDynamic_load
# projName = 'Nine-bus System'  # 'Nine-bus System'  Nine-bus System(1)_testDynamic_load

study_case = '08- Three Cycles IEEE Type 1 Mag-A_PSS'  # dont change this name  08- Three Cycles IEEE Type 1 Mag-A_PSS   03- Three Cycles Standard SG Model

# Activate the project
print(f'Activating project: {projName}')
project = app.ActivateProject(projName)
if project is None:
    raise Exception(f'Project "{projName}" not found or could not be activated')

# Activate the study case
print(f'Activating study case: {study_case}')
study_case_obj = app.GetProjectFolder('study').GetContents(study_case + '.IntCase')[0]
if study_case_obj is None:
    raise Exception(f'Study case "{study_case}" not found')
study_case_obj.Activate()

# Run load flow
ldf = app.GetFromStudyCase('ComLdf')
if ldf is None:
    print('Load flow command (ComLdf) not found.')
else:
    print('Executing load flow...')
    ldf.Execute()
    print('Load flow completed.')

# Print bus info
print('--- Bus Voltages and Power Injections ---')
buses = app.GetCalcRelevantObjects('*.ElmTerm')
print(f'Found {len(buses)} buses')
for bus in buses:
    try:
        voltage = bus.GetAttribute('m:u')
        angle = bus.GetAttribute('m:phiu')

        # Try different possible attribute names for power injection
        p_inj = None
        q_inj = None

        # List of possible attribute names for active power
        p_attributes = ['m:Psum', 'm:P', 'm:Pload', 'm:Ptot']
        for attr in p_attributes:
            try:
                p_inj = bus.GetAttribute(attr)
                if p_inj is not None:
                    print(f"Found active power using attribute: {attr}")
                    break
            except:
                continue

        # List of possible attribute names for reactive power
        q_attributes = ['m:Qsum', 'm:Q', 'm:Qload', 'm:Qtot']
        for attr in q_attributes:
            try:
                q_inj = bus.GetAttribute(attr)
                if q_inj is not None:
                    print(f"Found reactive power using attribute: {attr}")
                    break
            except:
                continue

        # Get generators connected to this bus
        generators = []
        try:
            cubicles = bus.GetCubicles()
            for cubicle in cubicles:
                if cubicle:
                    obj = cubicle.obj_id
                    if obj and hasattr(obj, 'GetClassName'):
                        if obj.GetClassName() in ['ElmSym', 'ElmGenstat']:  # Synchronous machines and static generators
                            generators.append(obj)
        except:
            pass

        # Get generation information
        gen_info = ""
        if generators:
            total_p_gen = 0
            total_q_gen = 0
            for gen in generators:
                try:
                    p_gen = gen.GetAttribute('m:P')
                    q_gen = gen.GetAttribute('m:Q')
                    if p_gen is not None:
                        total_p_gen += p_gen
                    if q_gen is not None:
                        total_q_gen += q_gen
                except:
                    pass

            if total_p_gen != 0 or total_q_gen != 0:
                gen_info = f", Generation: P = {total_p_gen:.2f} MW, Q = {total_q_gen:.2f} MVar"

        # If we have voltage but not power, just show what we have
        if p_inj is None and q_inj is None:
            print(f"Bus {bus.loc_name}: Voltage = {voltage:.4f} p.u., Angle = {angle:.2f} deg (Power data not available){gen_info}")
        elif p_inj is None:
            print(f"Bus {bus.loc_name}: Voltage = {voltage:.4f} p.u., Angle = {angle:.2f} deg, Q = {q_inj:.2f} MVar{gen_info}")
        elif q_inj is None:
            print(f"Bus {bus.loc_name}: Voltage = {voltage:.4f} p.u., Angle = {angle:.2f} deg, P = {p_inj:.2f} MW{gen_info}")
        else:
            print(f"Bus {bus.loc_name}: Voltage = {voltage:.4f} p.u., Angle = {angle:.2f} deg, P = {p_inj:.2f} MW, Q = {q_inj:.2f} MVar{gen_info}")
    except Exception as e:
        print(f"Error processing bus {bus.loc_name}: {str(e)}")

# Print line flow info
print('--- Line Power Flows ---')
lines = app.GetCalcRelevantObjects('*.ElmLne')
print(f'Found {len(lines)} lines')
for line in lines:
    try:
        # Try to get line flow attributes with error handling
        p1 = q1 = p2 = q2 = loading = None

        try:
            p1 = line.GetAttribute('m:P1')
        except:
            p1 = None
            
        try:
            q1 = line.GetAttribute('m:Q1')
        except:
            q1 = None
            
        try:
            p2 = line.GetAttribute('m:P2')
        except:
            p2 = None
            
        try:
            q2 = line.GetAttribute('m:Q2')
        except:
            q2 = None

        try:
            loading = line.GetAttribute('m:loading')
        except:
            loading = None

        # Print whatever information is available
        line_info = f"Line {line.loc_name}: "
        if p1 is not None and q1 is not None:
            line_info += f"P1 = {p1:.2f} MW, Q1 = {q1:.2f} MVar"
            if p2 is not None and q2 is not None:
                line_info += f", P2 = {p2:.2f} MW, Q2 = {q2:.2f} MVar"
        elif p1 is not None:
            line_info += f"P1 = {p1:.2f} MW"
            if p2 is not None:
                line_info += f", P2 = {p2:.2f} MW"
        elif q1 is not None:
            line_info += f"Q1 = {q1:.2f} MVar"
            if q2 is not None:
                line_info += f", Q2 = {q2:.2f} MVar"
        
        if loading is not None:
            line_info += f", Loading = {loading:.1f}%"

        print(line_info)
    except Exception as e:
        print(f"Error processing line {line.loc_name}: {str(e)}")
