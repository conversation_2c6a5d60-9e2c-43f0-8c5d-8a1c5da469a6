
### this one is  to sample the data according to the number of smaple and \\
# the simulation duration to decide the sample frequency

from datetime import datetime

import pandas as pd
import time
import sys
import os
import pandas as pd
import os

from fontTools.varLib.instancer.names import getVariationNameIDs


def process_mtl_file(mtl_path, timestep, master_csv_path):
    """
    Reads a .mtl-like file, adds a timestep column, and appends to a master CSV file.

    Args:
        mtl_path (str): Path to the .mtl file.
        timestep (int or float): Timestep value to add.
        master_csv_path (str): Path to the master CSV file to append to.
    """
    # Read the space-separated values into a DataFrame
    df = pd.read_csv(mtl_path, sep='\s+', header=None, names=['Index', 'Group', 'Value1', 'Value2'])

    # Add the Timestep column
    df['Timestep'] = timestep

    # Check if master CSV exists
    if os.path.exists(master_csv_path):
        # Append to existing CSV
        df.to_csv(master_csv_path, mode='a', header=False, index=False)
    else:
        # Create new CSV with headers
        df.to_csv(master_csv_path, index=False)

    print(f"Appended data from {mtl_path} (Timestep {timestep}) to {master_csv_path}")


# Example usage

######################

Dig_path = r"D:\\Program Files\\DIgSILENT\\PowerFactory 2024\\Python\\3.12"
sys.path.append(Dig_path)
os.environ['PATH'] += ';' + Dig_path

if __name__ == "__main__":
    import powerfactory as pf

app = pf.GetApplication()
if app is None:
    raise Exception('getting Powerfactory application failed')

# define project name and study case
projName = 'Nine-bus System'  # 'Nine-bus System'  Nine-bus System(1)_testDynamic_load



study_case = '08- Three Cycles IEEE Type 1 Mag-A_PSS'  # dont change this name  08- Three Cycles IEEE Type 1 Mag-A_PSS   03- Three Cycles Standard SG Model

# activate project
project = app.ActivateProject(projName)
proj = app.GetActiveProject()

# get the study case folder and activate project
oFolder_studycase = app.GetProjectFolder('study')  # dont change this folder name

oCase = oFolder_studycase.GetContents(study_case)[0]
oCase.Activate()
#
# grid_var = project.GetObject('Network Variants/IEEE Type 1 Mag-A')
# if grid_var:
#     grid_var.Activate()
#     print(f"Activated variation: {grid_var.loc_name}")
# else:
#     print("Variation not found.")

# Get EMT simulation command
sim = app.GetFromStudyCase('ComSim')  # Or use ComEMT if available

# Set simulation parameters
sim.iopt_sim = 1  # 1 = EMT simulation
sim.tstart = 0.0
sim.tstop = 4.0  # Run to 1 second
sim.dtgrd = 1e-5  # EMT timestep

# Get result bus
buses = app.GetCalcRelevantObjects('*.ElmTerm')  #
lines = app.GetCalcRelevantObjects('*.ElmLne')  #
generators = app.GetCalcRelevantObjects('*.ElmSym')  #

# Create a results object to store snapshots
results = app.GetFromStudyCase('*.ElmRes')
if results is None:
    results = app.CreateObject('ElmRes', 'Results')
    results.SetAttribute('loc_name', 'VoltageResults')

# Clear any existing results
results.Clear()

# Sampling setup
num_samples = 50  # Number of samples to take
sampling_interval = sim.tstop / num_samples  # Time between samples
data = pd.DataFrame()
modal_results = []

# Run multiple simulations, each time saving a snapshot and loading it for the next run
for i in range(num_samples):
    sample_time = i * sampling_interval
    sim.tstop = sample_time
    sim.Execute()

    row = {'Time': sample_time}
    for generator in generators:
        gP = generator.GetAttribute('s:P1')
        gQ = generator.GetAttribute('s:Q1')
        speed = generator.GetAttribute('s:speed')
        row[f'{generator.loc_name} P'] = gP
        row[f'{generator.loc_name} Q'] = gQ
        row[f'{generator.loc_name} speed'] = speed

    for bus in buses:  # read bus voltages
        vm = bus.GetAttribute('m:u1')
        va = bus.GetAttribute('m:phiu')
        row[f'{bus.loc_name} Vm'] = vm
        row[f'{bus.loc_name} V_angle'] = va

    # data = pd.concat([data, pd.DataFrame([row])], ignore_index=True)
    for line in lines:
        Im = line.GetAttribute('m:I1:bus1')
        Ia = line.GetAttribute('m:phii:bus1')
        row[f'{line.loc_name} Im'] = Im
        row[f'{line.loc_name} I_angle'] = Ia
    # Get modal analysis command
    modal = app.GetFromStudyCase('ComMod')
    if modal is None:
        print("Error: Could not get modal analysis command")
        continue

    try:

        # Configure modal analysis
        modal.iopt_mod = 1  # Back to integers since the string conversion didn't help
        modal.iopt_ev = 1
        modal.iopt_evsel = 0
        modal.iopt_evsort = 1
        modal.iopt_partfact = 1
        modal.iopt_evec = 1
        modal.iopt_met = 0
        modal.iopt_noevec = 0
        modal.iopt_which = 0

        modal_initMode = 0  # Synchronize with current state

        print("\nModal analysis configuration:")
        print(f"iopt_mod: {modal.iopt_mod}")
        print(f"iopt_ev: {modal.iopt_ev}")
        print(f"iopt_evsel: {modal.iopt_evsel}")
        print(f"iopt_evsort: {modal.iopt_evsort}")
        print(f"iopt_partfact: {modal.iopt_partfact}")
        print(f"iopt_evec: {modal.iopt_evec}")
        print(f"iopt_met: {modal.iopt_met}")
        print(f"iopt_noevec: {modal.iopt_noevec}")
        print(f"iopt_which: {modal.iopt_which}")

        # Perform modal analysis at this time step
        print(f"\nPerforming modal analysis at time {sample_time:.4f} s...")
        result = modal.Execute()
        print(f"Modal analysis execution result: {result}")
        mtl_path = r'C:\Users\<USER>\Downloads\modal_res\EVals.mtl'

        # process_mtl_file(mtl_path, timestep=sample_time, master_csv_path='Master_EVals.csv')

        # # Parse MTL file at this time step
        # eigenvalues = []
        # with open(mtl_path, 'r') as f:
        #     for line in f:
        #         parts = line.strip().split()
        #         eigval = complex(float(parts[1]), float(parts[2]))
        #         eigenvalues.append(eigval)
        # for idx, eig in enumerate(eigenvalues):
        #     row[f'EV_{idx}_real'] = eig.real
        #     row[f'EV_{idx}_imag'] = eig.imag
        #
        # data = pd.concat([data, pd.DataFrame([row])], ignore_index=True)



        #
        # # Read the space-separated values into a DataFrame
        # eigenvalues = pd.read_csv(mtl_path, sep=r'\s+', header=None, names=['Index', 'Group', 'Value1', 'Value2'])
        #
        # # Add the Timestep column
        # eigenvalues['Timestep'] = sample_time
        #
        # # Construct a new row with real and imaginary parts of eigenvalues
        # row = {'Timestep': sample_time}
        # for idx, (real, imag) in enumerate(zip(eigenvalues['Value1'], eigenvalues['Value2'])):
        #     row[f'EV_{idx}_real'] = real
        #     row[f'EV_{idx}_imag'] = imag
        #
        # # Convert to a DataFrame and append to the master CSV
        # new_row_df = pd.DataFrame([row])
        #
        #
        # data = pd.concat([data, pd.DataFrame([row])], ignore_index=True)






        # Read the space-separated values into a DataFrame
        eigenvalues = pd.read_csv(mtl_path, sep=r'\s+', header=None, names=['Index', 'Group', 'Value1', 'Value2'])

        # Add the Timestep column (optional if already in row)
        eigenvalues['Timestep'] = sample_time

        # Add eigenvalues to the existing row dictionary
        for idx, (real, imag) in enumerate(zip(eigenvalues['Value1'], eigenvalues['Value2'])):
            row[f'EV_{idx}_real'] = real
            row[f'EV_{idx}_imag'] = imag

        # Append to the main data DataFrame
        data = pd.concat([data, pd.DataFrame([row])], ignore_index=True)







    except Exception as e:
        print(f"Error in modal analysis at time {sample_time:.4f} s: {str(e)}")

    # If this isn't the last sample, reset the simulation for the next run
    if i < num_samples - 1:
        # Reset the simulation to initial conditions
        sim.tstop = 0.0
        sim.Execute()
        time.sleep(0.5)  # Give some time for the reset to complete
data.to_csv(
    f"modal_analysis data_cut5_working_{datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}.csv",
    index=False)
print(data)
