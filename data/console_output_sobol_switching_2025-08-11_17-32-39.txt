=== Starting Enhanced Modal Analysis with Sobol Sampling and Dynamic Switching ===
Found loads: ['Load C', 'Load B', 'Load A', 'Load C(1)', 'Load A(1)', 'Load B(1)']
Simulation setup:
- Number of scenarios: 2
- Samples per scenario: 5
- Simulation duration per scenario: 40.0 seconds
- Switching interval: 2.0 seconds
- Total switching events per scenario: 20
Generating Sobol samples for load scaling factors...
Generated 2 Sobol samples

=== Running Scenario 1/2 ===
Scaling factors - a: 1.139, b: 1.077, c: 1.176
Applying load scaling:
  Load A(1): P=0.000->0.000, Q=0.000->0.000 (scale=0.0114)
  Load B(1): P=0.000->0.000, Q=0.000->0.000 (scale=0.0108)
  Load C(1): P=0.000->0.000, Q=0.000->0.000 (scale=0.0118)
Generated 21 switching events

  --- Sample 1/5 at t=0.00s ---
  Applying switching state at t=0.0s:
    Load A(1): OFF (outserv=1)
    Load B(1): OFF (outserv=1)
    Load C(1): OFF (outserv=1)
    Warning: Could not read generator G2: 'DataObject' object has no attribute 's:P1'
    Warning: Could not read generator G3: 'DataObject' object has no attribute 's:P1'
    Warning: Could not read generator G1: 'DataObject' object has no attribute 's:P1'
    Warning: Could not read bus Bus 1: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 2: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 8: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 9: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 3: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 6: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 4: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 5: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 7: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read line Line 5-7: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 7-8: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 8-9: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 6-9: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 4-6: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 4-5: 'DataObject' object has no attribute 'm:I1:bus1'
    Performing modal analysis at time 0.0000 s...
    Modal analysis result: 0
    Successfully read 19 eigenvalues

  --- Sample 2/5 at t=8.00s ---
  Applying switching state at t=8.0s:
    Load A(1): ON (outserv=0)
    Load B(1): OFF (outserv=1)
    Load C(1): ON (outserv=0)
    Warning: Could not read generator G2: 'DataObject' object has no attribute 's:P1'
    Warning: Could not read generator G3: 'DataObject' object has no attribute 's:P1'
    Warning: Could not read generator G1: 'DataObject' object has no attribute 's:P1'
    Warning: Could not read bus Bus 1: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 2: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 8: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 9: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 3: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 6: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 4: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 5: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 7: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read line Line 5-7: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 7-8: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 8-9: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 6-9: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 4-6: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 4-5: 'DataObject' object has no attribute 'm:I1:bus1'
    Performing modal analysis at time 8.0000 s...
    Modal analysis result: 0
    Successfully read 19 eigenvalues

  --- Sample 3/5 at t=16.00s ---
  Applying switching state at t=16.0s:
    Load A(1): ON (outserv=0)
    Load B(1): OFF (outserv=1)
    Load C(1): ON (outserv=0)
    Warning: Could not read generator G2: 'DataObject' object has no attribute 's:P1'
    Warning: Could not read generator G3: 'DataObject' object has no attribute 's:P1'
    Warning: Could not read generator G1: 'DataObject' object has no attribute 's:P1'
    Warning: Could not read bus Bus 1: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 2: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 8: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 9: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 3: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 6: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 4: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 5: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 7: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read line Line 5-7: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 7-8: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 8-9: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 6-9: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 4-6: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 4-5: 'DataObject' object has no attribute 'm:I1:bus1'
    Performing modal analysis at time 16.0000 s...
    Modal analysis result: 0
    Successfully read 19 eigenvalues

  --- Sample 4/5 at t=24.00s ---
  Applying switching state at t=24.0s:
    Load A(1): OFF (outserv=1)
    Load B(1): OFF (outserv=1)
    Load C(1): OFF (outserv=1)
    Warning: Could not read generator G2: 'DataObject' object has no attribute 's:P1'
    Warning: Could not read generator G3: 'DataObject' object has no attribute 's:P1'
    Warning: Could not read generator G1: 'DataObject' object has no attribute 's:P1'
    Warning: Could not read bus Bus 1: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 2: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 8: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 9: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 3: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 6: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 4: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 5: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 7: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read line Line 5-7: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 7-8: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 8-9: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 6-9: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 4-6: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 4-5: 'DataObject' object has no attribute 'm:I1:bus1'
    Performing modal analysis at time 24.0000 s...
    Modal analysis result: 0
    Successfully read 19 eigenvalues

  --- Sample 5/5 at t=32.00s ---
  Applying switching state at t=32.0s:
    Load A(1): OFF (outserv=1)
    Load B(1): ON (outserv=0)
    Load C(1): ON (outserv=0)
    Warning: Could not read generator G2: 'DataObject' object has no attribute 's:P1'
    Warning: Could not read generator G3: 'DataObject' object has no attribute 's:P1'
    Warning: Could not read generator G1: 'DataObject' object has no attribute 's:P1'
    Warning: Could not read bus Bus 1: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 2: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 8: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 9: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 3: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 6: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 4: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 5: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 7: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read line Line 5-7: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 7-8: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 8-9: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 6-9: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 4-6: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 4-5: 'DataObject' object has no attribute 'm:I1:bus1'
    Performing modal analysis at time 32.0000 s...
    Modal analysis result: 0
    Successfully read 19 eigenvalues

=== Running Scenario 2/2 ===
Scaling factors - a: 0.795, b: 0.689, c: 0.677
Applying load scaling:
  Load A(1): P=0.000->0.000, Q=0.000->0.000 (scale=0.0080)
  Load B(1): P=0.000->0.000, Q=0.000->0.000 (scale=0.0069)
  Load C(1): P=0.000->0.000, Q=0.000->0.000 (scale=0.0068)
Generated 21 switching events

  --- Sample 1/5 at t=0.00s ---
  Applying switching state at t=0.0s:
    Load A(1): OFF (outserv=1)
    Load B(1): ON (outserv=0)
    Load C(1): OFF (outserv=1)
    Warning: Could not read generator G2: 'DataObject' object has no attribute 's:P1'
    Warning: Could not read generator G3: 'DataObject' object has no attribute 's:P1'
    Warning: Could not read generator G1: 'DataObject' object has no attribute 's:P1'
    Warning: Could not read bus Bus 1: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 2: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 8: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 9: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 3: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 6: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 4: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 5: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 7: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read line Line 5-7: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 7-8: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 8-9: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 6-9: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 4-6: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 4-5: 'DataObject' object has no attribute 'm:I1:bus1'
    Performing modal analysis at time 0.0000 s...
    Modal analysis result: 0
    Successfully read 19 eigenvalues

  --- Sample 2/5 at t=8.00s ---
  Applying switching state at t=8.0s:
    Load A(1): OFF (outserv=1)
    Load B(1): ON (outserv=0)
    Load C(1): ON (outserv=0)
    Warning: Could not read generator G2: 'DataObject' object has no attribute 's:P1'
    Warning: Could not read generator G3: 'DataObject' object has no attribute 's:P1'
    Warning: Could not read generator G1: 'DataObject' object has no attribute 's:P1'
    Warning: Could not read bus Bus 1: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 2: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 8: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 9: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 3: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 6: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 4: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 5: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 7: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read line Line 5-7: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 7-8: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 8-9: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 6-9: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 4-6: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 4-5: 'DataObject' object has no attribute 'm:I1:bus1'
    Performing modal analysis at time 8.0000 s...
    Modal analysis result: 0
    Successfully read 19 eigenvalues

  --- Sample 3/5 at t=16.00s ---
  Applying switching state at t=16.0s:
    Load A(1): OFF (outserv=1)
    Load B(1): ON (outserv=0)
    Load C(1): ON (outserv=0)
    Warning: Could not read generator G2: 'DataObject' object has no attribute 's:P1'
    Warning: Could not read generator G3: 'DataObject' object has no attribute 's:P1'
    Warning: Could not read generator G1: 'DataObject' object has no attribute 's:P1'
    Warning: Could not read bus Bus 1: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 2: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 8: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 9: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 3: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 6: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 4: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 5: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 7: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read line Line 5-7: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 7-8: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 8-9: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 6-9: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 4-6: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 4-5: 'DataObject' object has no attribute 'm:I1:bus1'
    Performing modal analysis at time 16.0000 s...
    Modal analysis result: 0
    Successfully read 19 eigenvalues

  --- Sample 4/5 at t=24.00s ---
  Applying switching state at t=24.0s:
    Load A(1): OFF (outserv=1)
    Load B(1): ON (outserv=0)
    Load C(1): OFF (outserv=1)
    Warning: Could not read generator G2: 'DataObject' object has no attribute 's:P1'
    Warning: Could not read generator G3: 'DataObject' object has no attribute 's:P1'
    Warning: Could not read generator G1: 'DataObject' object has no attribute 's:P1'
    Warning: Could not read bus Bus 1: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 2: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 8: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 9: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 3: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 6: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 4: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 5: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 7: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read line Line 5-7: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 7-8: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 8-9: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 6-9: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 4-6: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 4-5: 'DataObject' object has no attribute 'm:I1:bus1'
    Performing modal analysis at time 24.0000 s...
    Modal analysis result: 0
    Successfully read 19 eigenvalues

  --- Sample 5/5 at t=32.00s ---
  Applying switching state at t=32.0s:
    Load A(1): OFF (outserv=1)
    Load B(1): OFF (outserv=1)
    Load C(1): ON (outserv=0)
    Warning: Could not read generator G2: 'DataObject' object has no attribute 's:P1'
    Warning: Could not read generator G3: 'DataObject' object has no attribute 's:P1'
    Warning: Could not read generator G1: 'DataObject' object has no attribute 's:P1'
    Warning: Could not read bus Bus 1: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 2: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 8: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 9: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 3: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 6: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 4: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 5: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read bus Bus 7: 'DataObject' object has no attribute 'm:u1'
    Warning: Could not read line Line 5-7: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 7-8: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 8-9: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 6-9: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 4-6: 'DataObject' object has no attribute 'm:I1:bus1'
    Warning: Could not read line Line 4-5: 'DataObject' object has no attribute 'm:I1:bus1'
    Performing modal analysis at time 32.0000 s...
    Modal analysis result: 0
    Successfully read 19 eigenvalues

Completed all 2 scenarios!
Final results saved to data/modal_analysis_sobol_switching_complete_2_2025-08-11_17-32-52.csv
Total data points collected: 10
Data shape: (10, 47)

Summary:
- Scenarios processed: 2
- Time samples per scenario: 5
- Total data points: 10
- Scaling factor ranges: a=[0.795, 1.139], b=[0.689, 1.077], c=[0.677, 1.176]
