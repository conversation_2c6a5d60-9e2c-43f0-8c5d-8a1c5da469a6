### Enhanced with <PERSON><PERSON> sampling for load scaling and dynamic switching events
# Based on sample_table_new_output_2024.py structure with dynamic load disturbances

import pandas as pd
import time
import sys
import os
import traceback
import numpy as np
from scipy.stats import qmc
import random

def save_console_output(file_path, func, *args, **kwargs):
    """Save console output to file while also printing to console"""
    with open(file_path, 'w') as f:
        try:
            # Pass the file handle to the function
            func(f, *args, **kwargs)
        except Exception as e:
            error_msg = f"Error: {str(e)}"
            print(error_msg)  # Console
            print(error_msg, file=f)  # File
            traceback.print_exc()  # Console
            traceback.print_exc(file=f)  # File

def generate_sobol_samples(n_samples, n_dimensions=3, bounds=(0.5, 1.2)):
    """
    Generate Sobol samples for load scaling factors

    Args:
        n_samples: Number of samples to generate
        n_dimensions: Number of dimensions (3 for a, b, c)
        bounds: Tuple of (min, max) values for scaling factors

    Returns:
        Array of shape (n_samples, n_dimensions) with Sobol samples
    """
    sampler = qmc.Sobol(d=n_dimensions, scramble=True)
    samples = sampler.random(n_samples)
    # Scale from [0,1] to [bounds[0], bounds[1]]
    scaled_samples = qmc.scale(samples, bounds[0], bounds[1])
    return scaled_samples

def generate_switching_events(simulation_time, switch_interval=2.0):
    """
    Generate switching events for Load A(1), Load B(1), Load C(1) every 2 seconds
    Each load can be on (1) or off (0), creating 8 possible combinations

    Args:
        simulation_time: Total simulation time
        switch_interval: Time interval between switches (default 2.0 seconds)

    Returns:
        Dictionary with time points and corresponding switch states
    """
    n_switches = int(simulation_time / switch_interval)
    switching_events = {}

    for i in range(n_switches + 1):
        time_point = i * switch_interval
        if time_point <= simulation_time:
            # Randomly select one of 8 possible combinations (2^3)
            combination = random.randint(0, 7)  # 0 to 7 for 8 combinations
            load_a_state = (combination >> 0) & 1  # Extract bit 0
            load_b_state = (combination >> 1) & 1  # Extract bit 1
            load_c_state = (combination >> 2) & 1  # Extract bit 2

            switching_events[time_point] = {
                'Load A(1)': load_a_state,
                'Load B(1)': load_b_state,
                'Load C(1)': load_c_state
            }

    return switching_events

def apply_load_scaling(loads_dict, scaling_factors, output_file=None):
    """
    Apply scaling factors to loads

    Args:
        loads_dict: Dictionary of load objects
        scaling_factors: Dictionary with 'a', 'b', 'c' scaling factors
        output_file: File handle for dual output
    """
    def dual_print(message):
        print(message)  # Console
        if output_file:
            print(message, file=output_file)
            output_file.flush()

    load_scaling_map = {
        'Load A(1)': scaling_factors['a'] * 0.01,
        'Load B(1)': scaling_factors['b'] * 0.01,
        'Load C(1)': scaling_factors['c'] * 0.01
    }

    for load_name, scaling in load_scaling_map.items():
        if load_name in loads_dict:
            load_obj = loads_dict[load_name]
            try:
                # Get original values
                original_P = load_obj.GetAttribute('plini')  # Initial active power
                original_Q = load_obj.GetAttribute('qlini')  # Initial reactive power

                # Apply scaling
                new_P = original_P * scaling
                new_Q = original_Q * scaling

                load_obj.SetAttribute('plini', new_P)
                load_obj.SetAttribute('qlini', new_Q)

                dual_print(f"  {load_name}: P={original_P:.3f}->{new_P:.3f}, Q={original_Q:.3f}->{new_Q:.3f} (scale={scaling:.4f})")
            except Exception as e:
                dual_print(f"  Error scaling {load_name}: {str(e)}")

def apply_switching_state(loads_dict, switch_states, output_file=None):
    """
    Apply switching states to loads by controlling their outserv attribute

    Args:
        loads_dict: Dictionary of load objects
        switch_states: Dictionary with load names and their states (0/1)
        output_file: File handle for dual output
    """
    def dual_print(message):
        print(message)  # Console
        if output_file:
            print(message, file=output_file)
            output_file.flush()

    for load_name, state in switch_states.items():
        if load_name in loads_dict:
            load_obj = loads_dict[load_name]
            try:
                # Control the load by setting its outserv attribute
                # 0 = in service, 1 = out of service
                outserv_value = 1 - state  # Invert because outserv=1 means disconnected
                load_obj.SetAttribute('outserv', outserv_value)
                status = "ON" if state else "OFF"
                dual_print(f"    {load_name}: {status} (outserv={outserv_value})")
            except Exception as e:
                dual_print(f"    Error switching {load_name}: {str(e)}")

def process_mtl_file(mtl_path, timestep, master_csv_path):
    """
    Reads a .mtl-like file, adds a timestep column, and appends to a master CSV file.

    Args:
        mtl_path (str): Path to the .mtl file.
        timestep (int or float): Timestep value to add.
        master_csv_path (str): Path to the master CSV file to append to.
    """
    # Read the space-separated values into a DataFrame
    df = pd.read_csv(mtl_path, sep='\s+', header=None, names=['Index', 'Group', 'Value1', 'Value2'])

    # Add the Timestep column
    df['Timestep'] = timestep

    # Check if master CSV exists
    if os.path.exists(master_csv_path):
        # Append to existing CSV
        df.to_csv(master_csv_path, mode='a', header=False, index=False)
    else:
        # Create new CSV with headers
        df.to_csv(master_csv_path, index=False)

    print(f"Appended data from {mtl_path} (Timestep {timestep}) to {master_csv_path}")

def main(output_file=None):
    def dual_print(message):
        print(message)  # Console
        if output_file:
            print(message, file=output_file)
            output_file.flush()

    dual_print("=== Starting Enhanced Modal Analysis with Sobol Sampling and Dynamic Switching ===")

    # PowerFactory setup
    Dig_path = r"D:\\Program Files\\DIgSILENT\\PowerFactory 2024\\Python\\3.12"
    sys.path.append(Dig_path)
    os.environ['PATH'] += ';' + Dig_path

    import powerfactory as pf
    app = pf.GetApplication()
    if app is None:
        raise Exception('getting Powerfactory application failed')

    # define project name and study case
    projName = 'Nine-bus System'
    study_case = '08- Three Cycles IEEE Type 1 Mag-A_PSS'

    # activate project
    project = app.ActivateProject(projName)
    proj = app.GetActiveProject()

    # get the study case folder and activate project
    oFolder_studycase = app.GetProjectFolder('study')
    oCase = oFolder_studycase.GetContents(study_case)[0]
    oCase.Activate()

    # Get simulation command
    sim = app.GetFromStudyCase('ComSim')

    # Set simulation parameters
    sim.iopt_sim = 1  # EMT simulation
    sim.tstart = 0.0
    sim.tstop = 40.0  # Total simulation time
    sim.dtgrd = 1e-5   # EMT timestep

    # Get system objects
    buses = app.GetCalcRelevantObjects('*.ElmTerm')
    lines = app.GetCalcRelevantObjects('*.ElmLne')
    generators = app.GetCalcRelevantObjects('*.ElmSym')
    loads = app.GetCalcRelevantObjects('*.ElmLod')

    # Create load dictionary for easy access
    loads_dict = {load.loc_name: load for load in loads}
    dual_print(f"Found loads: {list(loads_dict.keys())}")

    # Results setup
    results = app.GetFromStudyCase('*.ElmRes')
    if results is None:
        results = app.CreateObject('ElmRes', 'Results')
        results.SetAttribute('loc_name', 'VoltageResults')
    results.Clear()

    # Enhanced sampling setup
    num_scenarios = 2  # Number of different scenarios
    num_samples_per_scenario = 5  # Number of time samples per scenario
    simulation_duration = 40.0  # Total time for each scenario
    switch_interval = 2.0  # Switch every 2 seconds

    dual_print(f"Simulation setup:")
    dual_print(f"- Number of scenarios: {num_scenarios}")
    dual_print(f"- Samples per scenario: {num_samples_per_scenario}")
    dual_print(f"- Simulation duration per scenario: {simulation_duration} seconds")
    dual_print(f"- Switching interval: {switch_interval} seconds")
    dual_print(f"- Total switching events per scenario: {int(simulation_duration / switch_interval)}")

    # Generate Sobol samples for load scaling factors (a, b, c)
    dual_print("Generating Sobol samples for load scaling factors...")
    sobol_samples = generate_sobol_samples(num_scenarios)
    dual_print(f"Generated {num_scenarios} Sobol samples")

    # Create master dataframe to store all results
    all_data = pd.DataFrame()

    # Ensure data folder exists
    os.makedirs('data', exist_ok=True)

    # Run scenarios with different initial conditions and switching patterns
    for scenario in range(num_scenarios):
        dual_print(f"\n=== Running Scenario {scenario + 1}/{num_scenarios} ===")

        # Get Sobol-sampled scaling factors for this scenario
        scaling_factors = {
            'a': sobol_samples[scenario, 0],
            'b': sobol_samples[scenario, 1],
            'c': sobol_samples[scenario, 2]
        }

        dual_print(f"Scaling factors - a: {scaling_factors['a']:.3f}, b: {scaling_factors['b']:.3f}, c: {scaling_factors['c']:.3f}")

        # Apply initial load scaling for this scenario
        dual_print("Applying load scaling:")
        apply_load_scaling(loads_dict, scaling_factors, output_file)

        # Generate switching events for this scenario
        switching_events = generate_switching_events(simulation_duration, switch_interval)
        dual_print(f"Generated {len(switching_events)} switching events")

        # Set up sampling points for this scenario
        sampling_interval = simulation_duration / num_samples_per_scenario

        # Process each time sample in this scenario
        for sample_idx in range(num_samples_per_scenario):
            sample_time = sample_idx * sampling_interval
            dual_print(f"\n  --- Sample {sample_idx + 1}/{num_samples_per_scenario} at t={sample_time:.2f}s ---")

            # Set simulation time and execute
            sim.tstop = sample_time
            sim.Execute()

            # Find the current switching state at this time
            current_switch_states = None
            for switch_time in sorted(switching_events.keys()):
                if switch_time <= sample_time:
                    current_switch_states = switching_events[switch_time]
                else:
                    break

            # Apply switching state if available
            if current_switch_states:
                dual_print(f"  Applying switching state at t={sample_time}s:")
                apply_switching_state(loads_dict, current_switch_states, output_file)

            # Collect data at this sample point
            row = {
                'Scenario': scenario,
                'Sample': sample_idx,
                'Time': sample_time,
                'ScalingA': scaling_factors['a'],
                'ScalingB': scaling_factors['b'],
                'ScalingC': scaling_factors['c']
            }

            # Add switching states to row
            if current_switch_states:
                row['LoadA_State'] = current_switch_states.get('Load A(1)', 1)
                row['LoadB_State'] = current_switch_states.get('Load B(1)', 1)
                row['LoadC_State'] = current_switch_states.get('Load C(1)', 1)
            else:
                row['LoadA_State'] = 1
                row['LoadB_State'] = 1
                row['LoadC_State'] = 1

            # Collect generator data
            for generator in generators:
                try:
                    gP = generator.GetAttribute('s:P1')
                    gQ = generator.GetAttribute('s:Q1')
                    speed = generator.GetAttribute('s:speed')
                    row[f'{generator.loc_name}_P'] = gP
                    row[f'{generator.loc_name}_Q'] = gQ
                    row[f'{generator.loc_name}_speed'] = speed
                except Exception as e:
                    dual_print(f"    Warning: Could not read generator {generator.loc_name}: {str(e)}")

            # Collect bus voltage data
            for bus in buses:
                try:
                    vm = bus.GetAttribute('m:u1')
                    va = bus.GetAttribute('m:phiu')
                    row[f'{bus.loc_name}_Vm'] = vm
                    row[f'{bus.loc_name}_V_angle'] = va
                except Exception as e:
                    dual_print(f"    Warning: Could not read bus {bus.loc_name}: {str(e)}")

            # Collect line current data
            for line in lines:
                try:
                    Im = line.GetAttribute('m:I1:bus1')
                    Ia = line.GetAttribute('m:phii:bus1')
                    row[f'{line.loc_name}_Im'] = Im
                    row[f'{line.loc_name}_I_angle'] = Ia
                except Exception as e:
                    dual_print(f"    Warning: Could not read line {line.loc_name}: {str(e)}")

            # Perform modal analysis
            modal = app.GetFromStudyCase('ComMod')
            if modal is None:
                dual_print("    Error: Could not get modal analysis command")
            else:
                try:
                    # Configure modal analysis
                    modal.iopt_mod = 1
                    modal.iopt_ev = 1
                    modal.iopt_evsel = 0
                    modal.iopt_evsort = 1
                    modal.iopt_partfact = 1
                    modal.iopt_evec = 1
                    modal.iopt_met = 0
                    modal.iopt_noevec = 0
                    modal.iopt_which = 0

                    dual_print(f"    Performing modal analysis at time {sample_time:.4f} s...")
                    result = modal.Execute()
                    dual_print(f"    Modal analysis result: {result}")

                    # Read eigenvalues if analysis succeeded
                    mtl_path = r'C:\Users\<USER>\Downloads\modal_res\EVals.mtl'
                    if os.path.exists(mtl_path):
                        eigenvalues_df = pd.read_csv(mtl_path, sep=r'\s+', header=None,
                                                   names=['Index', 'Group', 'Value1', 'Value2'])

                        for idx, (real, imag) in enumerate(zip(eigenvalues_df['Value1'], eigenvalues_df['Value2'])):
                            row[f'EV_{idx}_real'] = real
                            row[f'EV_{idx}_imag'] = imag

                        dual_print(f"    Successfully read {len(eigenvalues_df)} eigenvalues")
                    else:
                        dual_print(f"    Warning: MTL file not found at {mtl_path}")

                except Exception as e:
                    dual_print(f"    Error in modal analysis at time {sample_time:.4f} s: {str(e)}")

            # Add this data point to the master dataframe
            all_data = pd.concat([all_data, pd.DataFrame([row])], ignore_index=True)

            # Reset simulation for next sample
            if sample_idx < num_samples_per_scenario - 1:
                sim.tstop = 0.0
                sim.Execute()
                time.sleep(0.1)  # Small delay for stability

        # Save intermediate results every 100 scenarios
        if (scenario + 1) % 100 == 0:
            timestamp = time.strftime("%Y-%m-%d_%H-%M-%S")
            intermediate_filename = f"data/modal_analysis_sobol_switching_intermediate_{scenario+1}_{timestamp}.csv"
            all_data.to_csv(intermediate_filename, index=False)
            dual_print(f"Saved intermediate results to {intermediate_filename}")

    # Save final results
    timestamp = time.strftime("%Y-%m-%d_%H-%M-%S")
    final_filename = f"data/modal_analysis_sobol_switching_complete_{num_scenarios}_{timestamp}.csv"
    all_data.to_csv(final_filename, index=False)
    dual_print(f"\nCompleted all {num_scenarios} scenarios!")
    dual_print(f"Final results saved to {final_filename}")
    dual_print(f"Total data points collected: {len(all_data)}")
    dual_print(f"Data shape: {all_data.shape}")

    # Print summary statistics
    dual_print(f"\nSummary:")
    dual_print(f"- Scenarios processed: {num_scenarios}")
    dual_print(f"- Time samples per scenario: {num_samples_per_scenario}")
    dual_print(f"- Total data points: {len(all_data)}")
    dual_print(f"- Scaling factor ranges: a=[{sobol_samples[:, 0].min():.3f}, {sobol_samples[:, 0].max():.3f}], b=[{sobol_samples[:, 1].min():.3f}, {sobol_samples[:, 1].max():.3f}], c=[{sobol_samples[:, 2].min():.3f}, {sobol_samples[:, 2].max():.3f}]")


if __name__ == "__main__":
    timestamp = time.strftime("%Y-%m-%d_%H-%M-%S")
    output_filename = f'data/console_output_sobol_switching_{timestamp}.txt'

    # Ensure data folder exists
    os.makedirs('data', exist_ok=True)

    save_console_output(output_filename, main)
