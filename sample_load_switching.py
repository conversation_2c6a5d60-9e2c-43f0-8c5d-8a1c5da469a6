import random
import time
import pandas as pd
import sys
import os
from datetime import datetime

def get_load_switches(app):
    """
    Get the switch objects associated with Load A(1), Load B(1), and Load C(1)
    """
    # Try to get switches by name or by searching for loads and their associated switches
    switches = []
    
    # Search for switches in the project
    all_switches = app.GetCalcRelevantObjects('*.ElmSwitch')
    
    # Filter switches connected to loads A(1), B(1), and C(1)
    load_names = ['Load A(1)', 'Load B(1)', 'Load C(1)']
    for switch in all_switches:
        # Try to identify switches connected to the specific loads
        # This is a generic approach - in practice, you might need to adjust based on your model
        switch_name = switch.loc_name
        for load_name in load_names:
            if load_name.replace(' ', '_').replace('(1)', '') in switch_name or \
               load_name.replace(' ', '').replace('(1)', '') in switch_name:
                switches.append({
                    'switch': switch,
                    'load_name': load_name,
                    'name': switch_name
                })
    
    # If we couldn't find switches by name matching, try another approach
    if len(switches) < 3:
        # Reset and try to get all loads and their connected switches
        switches = []
        for load_name in load_names:
            loads = app.GetCalcRelevantObjects(f'*{load_name}*')
            for load in loads:
                # Try to find switches connected to this load
                # This is a simplified approach - in a real system, you'd need to trace the topology
                for switch in all_switches:
                    switches.append({
                        'switch': switch,
                        'load_name': load_name,
                        'name': switch.loc_name
                    })
    
    return switches[:3] if len(switches) >= 3 else switches

def toggle_switch(switch_obj, state):
    """
    Toggle a switch on or off
    state: True for on (closed), False for off (open)
    """
    if switch_obj:
        switch_obj.on_off = 1 if state else 0  # 1 = closed/on, 0 = open/off
        status = "ON" if state else "OFF"
        print(f"Switch {switch_obj.loc_name} turned {status}")

def random_switching_event(switches):
    """
    Randomly turn on or off each switch
    """
    print(f"\n--- Random Switching Event at {datetime.now().strftime('%H:%M:%S')} ---")
    for switch_info in switches:
        switch_obj = switch_info['switch']
        load_name = switch_info['load_name']
        # Randomly decide to turn on (True) or off (False)
        new_state = random.choice([True, False])
        toggle_switch(switch_obj, new_state)
        print(f"  Load {load_name}: {'ON' if new_state else 'OFF'}")
    print("--- End of Switching Event ---\n")

def collect_system_data(app, generators, buses, switches, event_time, event_id):
    """
    Collect system data after a switching event
    """
    row = {
        'Event_ID': event_id,
        'Event_Time': event_time,
        'Timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }
    
    # Add switch states
    for switch_info in switches:
        switch_obj = switch_info['switch']
        load_name = switch_info['load_name']
        row[f'{load_name}_Status'] = 'ON' if switch_obj.on_off == 1 else 'OFF'
    
    # Collect generator data
    for generator in generators:
        try:
            gP = generator.GetAttribute('s:P1')
            gQ = generator.GetAttribute('s:Q1')
            speed = generator.GetAttribute('s:speed')
            row[f'{generator.loc_name}_P'] = gP
            row[f'{generator.loc_name}_Q'] = gQ
            row[f'{generator.loc_name}_Speed'] = speed
        except:
            row[f'{generator.loc_name}_P'] = 0.0
            row[f'{generator.loc_name}_Q'] = 0.0
            row[f'{generator.loc_name}_Speed'] = 0.0
    
    # Collect bus data
    for bus in buses:
        try:
            vm = bus.GetAttribute('m:u1')
            va = bus.GetAttribute('m:phiu')
            row[f'{bus.loc_name}_Vm'] = vm
            row[f'{bus.loc_name}_V_angle'] = va
        except:
            row[f'{bus.loc_name}_Vm'] = 0.0
            row[f'{bus.loc_name}_V_angle'] = 0.0
            
    return row

if __name__ == "__main__":
    Dig_path = r"D:\Program Files\DIgSILENT\PowerFactory 2024\Python\3.12"
    sys.path.append(Dig_path)
    os.environ['PATH'] += ';' + Dig_path
    
    import powerfactory as pf
    
    app = pf.GetApplication()
    if app is None:
        raise Exception('Getting PowerFactory application failed')
    
    # Define project name and study case
    projName = 'Nine-bus System'
    study_case = '08- Three Cycles IEEE Type 1 Mag-A_PSS'
    
    # Activate project
    project = app.ActivateProject(projName)
    proj = app.GetActiveProject()
    
    # Get the study case folder and activate study case
    oFolder_studycase = app.GetProjectFolder('study')
    oCase = oFolder_studycase.GetContents(study_case)[0]
    oCase.Activate()
    
    # Get simulation command
    sim = app.GetFromStudyCase('ComSim')
    
    # Get relevant objects
    generators = app.GetCalcRelevantObjects('*.ElmSym')
    buses = app.GetCalcRelevantObjects('*.ElmTerm')
    
    # Get load switches
    switches = get_load_switches(app)
    
    if len(switches) < 3:
        print(f"Warning: Only found {len(switches)} switches, expected 3 for Load A(1), B(1), C(1)")
        print("Please check your model or adjust the switch identification logic")
    
    print(f"Found {len(switches)} load switches:")
    for switch_info in switches:
        print(f"  - {switch_info['name']} for {switch_info['load_name']}")
    
    # Simulation parameters
    total_duration = 100.0  # seconds
    event_interval = 10.0    # seconds
    num_events = int(total_duration / event_interval)
    
    print(f"\nStarting simulation for {total_duration} seconds with switching events every {event_interval} seconds")
    print(f"Total events: {num_events}")
    
    # Initialize simulation
    sim.tstart = 0.0
    sim.tstop = 0.0
    
    # Data collection
    results_data = pd.DataFrame()
    
    # Main simulation loop
    for event_id in range(num_events + 1):
        event_time = event_id * event_interval
        print(f"\n{'='*60}")
        print(f"Event {event_id} at time {event_time} seconds")
        print(f"{'='*60}")
        
        # For the first event (event_id=0), just collect initial state without switching
        if event_id > 0:
            # Perform random switching event
            random_switching_event(switches)
        
        # Run simulation for event_interval seconds
        sim.tstart = event_time
        sim.tstop = event_time + event_interval
        result = sim.Execute()
        
        if result != 0:
            print(f"Warning: Simulation returned error code {result}")
        
        # Collect system data after the switching event
        data_row = collect_system_data(app, generators, buses, switches, event_time, event_id)
        results_data = pd.concat([results_data, pd.DataFrame([data_row])], ignore_index=True)
        
        # Small delay to ensure simulation completes
        time.sleep(0.5)
    
    # Save results
    filename = f"load_switching_results_{datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}.csv"
    results_data.to_csv(filename, index=False)
    print(f"\nResults saved to: {filename}")
    print(f"Total events processed: {num_events + 1}")