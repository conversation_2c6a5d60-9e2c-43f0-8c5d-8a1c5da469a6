from datetime import datetime
import pandas as pd
import time
import sys
import os

def get_all_elements_and_create_cases(app):
    """
    Create N-1 contingency cases by disconnecting each line and transformer one at a time
    Returns a list of case descriptions
    """
    # Get transmission lines and transformers separately
    lines = app.GetCalcRelevantObjects('*.ElmLne')  # Transmission lines
    transformers = app.GetCalcRelevantObjects('*.ElmTr2')  # Two-winding transformers
     
    # Combine all transmission elements
    all_elements = list(lines) + list(transformers)
     
    cases = []

    # Case 0: Base case (all elements connected)
    cases.append({
        'name': 'Base_Case_Full_Network',
        'description': 'All lines and transformers connected',
        'disconnected_element': None,
        'element_type': None
    })
 
    # Cases 1-N: Each element disconnected
    for i, element in enumerate(all_elements):
        element_type = "Line" if hasattr(element, 'typ_id') and 'ElmLne' in str(type(element)) else "Transformer"
        cases.append({
            'name': f'N-1_Case_{i+1}_{element.loc_name}',
            'description': f'{element_type} {element.loc_name} disconnected',
            'disconnected_element': element,
            'element_type': element_type
        })
 
    return cases, lines, transformers
 
def disconnect_element(element, element_type):
    """Disconnect a line or transformer by setting it out of service"""
    if element:
        element.outserv = 1  # 1 = out of service, 0 = in service
        print(f"Disconnected {element_type.lower()}: {element.loc_name}")
 
def reconnect_all_elements(lines, transformers):
    """Reconnect all lines and transformers"""
    for line in lines:
        line.outserv = 0  # 0 = in service
    for transformer in transformers:
        transformer.outserv = 0  # 0 = in service
    print("All lines and transformers reconnected")

if __name__ == "__main__":
    Dig_path = r"D:\Program Files\DIgSILENT\PowerFactory 2024\Python\3.12"
    sys.path.append(Dig_path)
    os.environ['PATH'] += ';' + Dig_path
    
    import powerfactory as pf

    app = pf.GetApplication()
    if app is None:
        raise Exception('getting Powerfactory application failed')

    # define project name and study case
    projName = 'Nine-bus System'
    study_case = '08- Three Cycles IEEE Type 1 Mag-A_PSS'

    # activate project
    project = app.ActivateProject(projName)
    proj = app.GetActiveProject()

    # get the study case folder and activate project
    oFolder_studycase = app.GetProjectFolder('study')
    oCase = oFolder_studycase.GetContents(study_case)[0]
    oCase.Activate()

    # Get EMT simulation command
    sim = app.GetFromStudyCase('ComSim')

    # Get relevant objects
    generators = app.GetCalcRelevantObjects('*.ElmSym')
    buses = app.GetCalcRelevantObjects('*.ElmTerm')

    # Sampling setup
    num_samples = 100
    sampling_interval = 20.0 / num_samples

    # Get all transmission elements and create contingency cases
    contingency_cases, lines, transformers = get_all_elements_and_create_cases(app)
    all_elements = list(lines) + list(transformers)

    print(f"Found {len(lines)} transmission lines and {len(transformers)} transformers")
    print(f"Total cases to analyze: {len(contingency_cases)}")

    # Print element details
    print("\nTransmission Lines:")
    for i, line in enumerate(lines):
        print(f"  {i+1}. {line.loc_name}")

    print("\nTransformers:")
    for i, transformer in enumerate(transformers):
        print(f"  {i+1}. {transformer.loc_name}")

    # Main loop for each contingency case


    # all_case_data = pd.DataFrame()

    for case_idx, case_info in enumerate(contingency_cases):
        print(f"\n{'='*60}")
        print(f"Running Case {case_idx}: {case_info['name']}")
        print(f"Description: {case_info['description']}")
        print(f"{'='*60}")

        # Reset all elements to connected state first
        reconnect_all_elements(lines, transformers)

        # Apply contingency (disconnect specific element if not base case)
        if case_info['disconnected_element'] is not None:
            disconnect_element(case_info['disconnected_element'], case_info['element_type'])

        # Initialize load flow to ensure system is solvable
        ldf = app.GetFromStudyCase('ComLdf')
        if ldf:
            ldf_result = ldf.Execute()
            if ldf_result != 0:
                print(f"Warning: Load flow failed for case {case_info['name']}")
                print("System may be unsolvable with this contingency")
                continue

        # Reset simulation parameters for this case
        sim.tstart = 0.0
        sim.tstop = 100.0

        # Data collection for this case
        case_data = pd.DataFrame()

        # Your existing sampling loop
        for i in range(num_samples):
            sample_time = i * sampling_interval
            sim.tstop = sample_time
            sim.Execute()

            row = {
                'Case': case_info['name'],
                'Case_Description': case_info['description'],
                'Element_Type': case_info['element_type'] if case_info['element_type'] else 'Base_Case',
                'Time': sample_time
            }

            # Collect generator data
            for generator in generators:
                gP = generator.GetAttribute('s:P1')
                gQ = generator.GetAttribute('s:Q1')
                speed = generator.GetAttribute('s:speed')
                row[f'{generator.loc_name} P'] = gP
                row[f'{generator.loc_name} Q'] = gQ
                row[f'{generator.loc_name} speed'] = speed

            # Collect bus data
            for bus in buses:
                # print(dir(bus))  # List all attributes/methods of the object

                vm = bus.GetAttribute('m:u1')
                va = bus.GetAttribute('m:phiu')
                row[f'{bus.loc_name} Vm'] = vm
                row[f'{bus.loc_name} V_angle'] = va

            # Collect line data (only for lines that are in service)
            for line in lines:
                if line.outserv == 0:  # Only collect data from lines in service
                    Im = line.GetAttribute('m:I1:bus1')
                    Ia = line.GetAttribute('m:phii:bus1')
                    row[f'{line.loc_name} Im'] = Im
                    row[f'{line.loc_name} I_angle'] = Ia
                else:
                    row[f'{line.loc_name} Im'] = 0.0
                    row[f'{line.loc_name} I_angle'] = 0.0

            # Collect transformer data
            for transformer in transformers:
                if transformer.outserv == 0:  # Only collect data from transformers in service
                    try:
                        Im_tf = transformer.GetAttribute('m:I1:bushv')  # High voltage side current
                        Ia_tf = transformer.GetAttribute('m:phii:bushv')
                        row[f'{transformer.loc_name} Im'] = Im_tf
                        row[f'{transformer.loc_name} I_angle'] = Ia_tf
                    except:
                        row[f'{transformer.loc_name} Im'] = 0.0
                        row[f'{transformer.loc_name} I_angle'] = 0.0
                else:
                    row[f'{transformer.loc_name} Im'] = 0.0
                    row[f'{transformer.loc_name} I_angle'] = 0.0

            # Your existing modal analysis code
            modal = app.GetFromStudyCase('ComMod')
            if modal is not None:
                try:
                    modal.iopt_mod = 1
                    modal.iopt_ev = 1
                    modal.iopt_evsel = 0
                    modal.iopt_evsort = 1
                    modal.iopt_partfact = 1
                    modal.iopt_evec = 1
                    modal.iopt_met = 0
                    modal.iopt_noevec = 0
                    modal.iopt_which = 0

                    result = modal.Execute()
                    mtl_path = r'C:\Users\<USER>\Downloads\modal_res\EVals.mtl'

                    if os.path.exists(mtl_path):
                        eigenvalues = pd.read_csv(mtl_path, sep='\s+', header=None,
                                                names=['Index', 'Group', 'Value1', 'Value2'])

                        for idx, (real, imag) in enumerate(zip(eigenvalues['Value1'], eigenvalues['Value2'])):
                            row[f'EV_{idx}_real'] = real
                            row[f'EV_{idx}_imag'] = imag

                except Exception as e:
                    print(f"Error in modal analysis for case {case_info['name']} at time {sample_time}: {str(e)}")

            # Append row to case data
            case_data = pd.concat([case_data, pd.DataFrame([row])], ignore_index=True)

            # Reset simulation for next sample if not last sample
            if i < num_samples - 1:
                sim.tstop = 0.0
                sim.Execute()
                time.sleep(0.5)

        # Append case data to overall results


        # all_case_data = pd.concat([all_case_data, case_data], ignore_index=True)

        # Save intermediate results for this case
        case_filename = f"N-1_analysis_{case_info['name']}_{datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}.csv"
        case_data.to_csv(case_filename, index=False)
        print(f"Saved case data to: {case_filename}")

    # Restore all elements before finishing
    reconnect_all_elements(lines, transformers)

    # Save final combined results
    # final_filename = f"N-1_complete_analysis_{datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}.csv"
    # all_case_data.to_csv(final_filename, index=False)
    # print(f"\nFinal combined results saved to: {final_filename}")
    print(f"Total cases analyzed: {len(contingency_cases)}")
    print(f"Lines: {len(lines)}, Transformers: {len(transformers)}, Total elements: {len(all_elements)}")
