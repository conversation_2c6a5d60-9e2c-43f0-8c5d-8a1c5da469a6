import pandas as pd
import time
import sys
import os
import traceback
import random
from datetime import datetime


def save_console_output(file_path, func, *args, **kwargs):
    with open(file_path, 'w') as f:
        try:
            # Pass the file handle to the function
            func(f, *args, **kwargs)
        except Exception as e:
            error_msg = f"Error: {str(e)}"
            print(error_msg)  # Console
            print(error_msg, file=f)  # File
            traceback.print_exc()  # Console
            traceback.print_exc(file=f)  # File


def sobol_sample(min_val, max_val, n_samples):
    """
    Generate Sobol sequence samples between min_val and max_val
    For simplicity, using random sampling here instead of true Sobol sequence
    """
    return [random.uniform(min_val, max_val) for _ in range(n_samples)]


def get_load_switches(app):
    """
    Get the switch objects associated with Load A(1), Load B(1), and Load C(1)
    """
    # Try to get switches by name or by searching for loads and their associated switches
    switches = []
    
    # Search for switches in the project
    all_switches = app.GetCalcRelevantObjects('*.ElmSwitch')
    
    # Filter switches connected to loads A(1), B(1), and C(1)
    load_names = ['Load A(1)', 'Load B(1)', 'Load C(1)']
    for switch in all_switches:
        # Try to identify switches connected to the specific loads
        switch_name = switch.loc_name
        for load_name in load_names:
            if load_name.replace(' ', '_').replace('(1)', '') in switch_name or \
               load_name.replace(' ', '').replace('(1)', '') in switch_name:
                switches.append({
                    'switch': switch,
                    'load_name': load_name,
                    'name': switch_name
                })
    
    # If we couldn't find switches by name matching, try another approach
    if len(switches) < 3:
        # Reset and try to get all loads and their connected switches
        switches = []
        for load_name in load_names:
            loads = app.GetCalcRelevantObjects(f'*{load_name}*')
            for load in loads:
                # Try to find switches connected to this load
                for switch in all_switches:
                    switches.append({
                        'switch': switch,
                        'load_name': load_name,
                        'name': switch.loc_name
                    })
    
    return switches[:3] if len(switches) >= 3 else switches


def toggle_switch(switch_obj, state):
    """
    Toggle a switch on or off
    state: True for on (closed), False for off (open)
    """
    if switch_obj:
        switch_obj.on_off = 1 if state else 0  # 1 = closed/on, 0 = open/off
        status = "ON" if state else "OFF"
        print(f"Switch {switch_obj.loc_name} turned {status}")


def random_switching_event(switches):
    """
    Randomly turn on or off each switch
    Returns the states of switches
    """
    print(f"\n--- Random Switching Event at {datetime.now().strftime('%H:%M:%S')} ---")
    states = {}
    for switch_info in switches:
        switch_obj = switch_info['switch']
        load_name = switch_info['load_name']
        # Randomly decide to turn on (True) or off (False)
        new_state = random.choice([True, False])
        toggle_switch(switch_obj, new_state)
        states[load_name] = new_state
        print(f"  Load {load_name}: {'ON' if new_state else 'OFF'}")
    print("--- End of Switching Event ---\n")
    return states


def set_load_scaling(app, a, b, c):
    """
    Set the scaling factors for loads
    a, b, c are the Sobol sampled parameters for Load A, B, C respectively
    Load A(1), B(1), C(1) get scaling factors a*0.01, b*0.01, c*0.01 respectively
    """
    # Get all loads
    loads = app.GetCalcRelevantObjects('*.ElmLod')
    
    # Set scaling factors
    for load in loads:
        load_name = load.loc_name
        if load_name == 'Load A(1)':
            load.scale0 = a * 0.01
        elif load_name == 'Load B(1)':
            load.scale0 = b * 0.01
        elif load_name == 'Load C(1)':
            load.scale0 = c * 0.01
        elif load_name == 'Load A':
            load.scale0 = a
        elif load_name == 'Load B':
            load.scale0 = b
        elif load_name == 'Load C':
            load.scale0 = c
    
    print(f"Load scaling factors set: a={a:.4f}, b={b:.4f}, c={c:.4f}")
    print(f"Load A(1) scaling: {a*0.01:.4f}, Load B(1) scaling: {b*0.01:.4f}, Load C(1) scaling: {c*0.01:.4f}")


def collect_system_data(app, generators, buses, lines, switches, event_time, event_id, load_params, switch_states):
    """
    Collect system data 
    """
    row = {
        'Event_ID': event_id,
        'Event_Time': event_time,
        'Timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'Load_A_factor': load_params[0],
        'Load_B_factor': load_params[1],
        'Load_C_factor': load_params[2]
    }
    
    # Add switch states
    for switch_info in switches:
        switch_obj = switch_info['switch']
        load_name = switch_info['load_name']
        row[f'{load_name}_Status'] = 'ON' if switch_obj.on_off == 1 else 'OFF'
    
    # Collect generator data
    for generator in generators:
        try:
            gP = generator.GetAttribute('s:P1')
            gQ = generator.GetAttribute('s:Q1')
            speed = generator.GetAttribute('s:speed')
            row[f'{generator.loc_name}_P'] = gP
            row[f'{generator.loc_name}_Q'] = gQ
            row[f'{generator.loc_name}_Speed'] = speed
        except:
            row[f'{generator.loc_name}_P'] = 0.0
            row[f'{generator.loc_name}_Q'] = 0.0
            row[f'{generator.loc_name}_Speed'] = 0.0
    
    # Collect bus data
    for bus in buses:
        try:
            vm = bus.GetAttribute('m:u1')
            va = bus.GetAttribute('m:phiu')
            row[f'{bus.loc_name}_Vm'] = vm
            row[f'{bus.loc_name}_V_angle'] = va
        except:
            row[f'{bus.loc_name}_Vm'] = 0.0
            row[f'{bus.loc_name}_V_angle'] = 0.0
            
    # Collect line data
    for line in lines:
        try:
            Im = line.GetAttribute('m:I1:bus1')
            Ia = line.GetAttribute('m:phii:bus1')
            row[f'{line.loc_name}_Im'] = Im
            row[f'{line.loc_name}_I_angle'] = Ia
        except:
            row[f'{line.loc_name}_Im'] = 0.0
            row[f'{line.loc_name}_I_angle'] = 0.0
            
    return row


def main(output_file=None):
    def dual_print(message):
        print(message)  # Console
        if output_file:
            print(message, file=output_file)
            output_file.flush()

    dual_print("Starting load sampling with initial load scaling and dynamic switching disturbance")

    Dig_path = r"D:\Program Files\DIgSILENT\PowerFactory 2024\Python\3.12"
    sys.path.append(Dig_path)
    os.environ['PATH'] += ';' + Dig_path

    import powerfactory as pf
    app = pf.GetApplication()
    if app is None:
        raise Exception('getting Powerfactory application failed')

    # define project name and study case
    projName = 'Nine-bus System'
    study_case = '08- Three Cycles IEEE Type 1 Mag-A_PSS'

    # activate project
    project = app.ActivateProject(projName)
    proj = app.GetActiveProject()

    # get the study case folder and activate project
    oFolder_studycase = app.GetProjectFolder('study')
    oCase = oFolder_studycase.GetContents(study_case)[0]
    oCase.Activate()

    sim = app.GetFromStudyCase('ComSim')

    sim.iopt_sim = 1
    sim.tstart = 0.0
    sim.tstop = 100.0
    sim.dtgrd = 1e-5

    buses = app.GetCalcRelevantObjects('*.ElmTerm')
    lines = app.GetCalcRelevantObjects('*.ElmLne')
    generators = app.GetCalcRelevantObjects('*.ElmSym')

    # Get load switches
    switches = get_load_switches(app)
    
    if len(switches) < 3:
        dual_print(f"Warning: Only found {len(switches)} switches, expected 3 for Load A(1), B(1), C(1)")
        dual_print("Please check your model or adjust the switch identification logic")
    
    dual_print(f"Found {len(switches)} load switches:")
    for switch_info in switches:
        dual_print(f"  - {switch_info['name']} for {switch_info['load_name']}")

    # Generate Sobol samples for load scaling factors
    # For now, just 2 simulations as requested
    n_simulations = 2
    a_samples = sobol_sample(0.5, 1.2, n_simulations)
    b_samples = sobol_sample(0.5, 1.2, n_simulations)
    c_samples = sobol_sample(0.5, 1.2, n_simulations)
    
    # Data collection
    all_data = pd.DataFrame()
    
    # Run simulations
    for sim_idx in range(n_simulations):
        dual_print(f"\n{'='*60}")
        dual_print(f"Simulation {sim_idx+1}/{n_simulations}")
        dual_print(f"{'='*60}")
        
        # Set load scaling factors
        a = a_samples[sim_idx]
        b = b_samples[sim_idx]
        c = c_samples[sim_idx]
        set_load_scaling(app, a, b, c)
        
        # Reset simulation
        sim.tstart = 0.0
        sim.tstop = 0.0
        sim.Execute()
        time.sleep(0.5)
        
        # Run simulation with switching events every 2 seconds
        total_duration = 20.0  # seconds
        event_interval = 2.0    # seconds
        num_events = int(total_duration / event_interval)
        
        dual_print(f"Starting simulation for {total_duration} seconds with switching events every {event_interval} seconds")
        dual_print(f"Total events: {num_events}")
        
        # Main simulation loop
        for event_id in range(num_events + 1):
            event_time = event_id * event_interval
            dual_print(f"\n{'-'*40}")
            dual_print(f"Event {event_id} at time {event_time} seconds")
            dual_print(f"{'-'*40}")
            
            # For the first event (event_id=0), just collect initial state without switching
            switch_states = {}
            if event_id > 0:
                # Perform random switching event
                switch_states = random_switching_event(switches)
            
            # Run simulation for event_interval seconds with 0.02s snapshots
            sim.tstart = event_time
            sim.tstop = event_time + event_interval
            result = sim.Execute()
            
            if result != 0:
                dual_print(f"Warning: Simulation returned error code {result}")
            
            # Sample data every 0.02 seconds within this interval
            interval_duration = event_interval
            sampling_interval = 0.02  # As requested
            num_samples = int(interval_duration / sampling_interval)
            
            for sample_idx in range(num_samples + 1):
                sample_time = event_time + sample_idx * sampling_interval
                
                # If we've reached the end time, break
                if sample_time > event_time + interval_duration:
                    break
                    
                # For this implementation, we'll collect data at each switching event point
                # In a more detailed implementation, we could interpolate or run shorter simulations
                
                if sample_idx % int(0.2/sampling_interval) == 0 or sample_idx == 0:  # Sample less frequently for data collection
                    # Collect system data
                    data_row = collect_system_data(
                        app, generators, buses, lines, switches, 
                        sample_time, f"{sim_idx+1}-{event_id}-{sample_idx}", 
                        (a, b, c), switch_states)
                    all_data = pd.concat([all_data, pd.DataFrame([data_row])], ignore_index=True)
            
            # Small delay to ensure simulation completes
            time.sleep(0.1)
            
        # Save intermediate results
        intermediate_filename = f"load_sampling_results_simulation_{sim_idx+1}_{datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}.csv"
        all_data.to_csv(intermediate_filename, index=False)
        dual_print(f"Intermediate results saved to {intermediate_filename}")
    
    # Save final results
    final_filename = f"load_sampling_results_final_{datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}.csv"
    all_data.to_csv(final_filename, index=False)
    dual_print(f"\nFinal results saved to {final_filename}")
    dual_print(f"Total data points collected: {len(all_data)}")
    dual_print(str(all_data.head()))


if __name__ == "__main__":
    save_console_output(f'load_sampling_console_output_{datetime.now().strftime("%Y-%m-%d_%H-%M-%S")}.txt', main)