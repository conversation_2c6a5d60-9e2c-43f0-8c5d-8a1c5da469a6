import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from pydmd import (DMD, FbDMD, HODMD, HankelDMD, BOPDMD,
                   DMDc, OptDMD, RDMD, CDMD)

# 加载数据
# df = pd.read_csv('modal_analysis data_cut5_working.csv', skiprows=range(4000, 5000), nrows=1000)

# df = pd.read_csv('modal_analysis data_cut5_working.csv')

# df = pd.read_csv('modal_analysis data_cut5_working_2025-08-08_16-57-47.csv',skiprows=range(1, 250), nrows=750)
# df = pd.read_csv('modal_analysis data_cut5_working_2025-08-09_03-55-53.csv',skiprows=range(1, 550), nrows=1450)

# df = pd.read_csv('modal_analysis data_cut5_reproduce_stable100.csv', skiprows=range(4000,5000), nrows=1000)
# df = pd.read_csv('modal_analysis data_cut5_working_2025-08-09_10-57-04.csv', skiprows=range(4000,5000), nrows=1000)
# df = pd.read_csv('modal_analysis data_cut5_working_2025-08-09_10-57-04.csv',nrows=1000)
# df = pd.read_csv('modal_analysis data_cut5_working_2025-08-09_15-44-18.csv',skiprows=range(4000,5000), nrows=1000)


# df = pd.read_csv('modal_analysis data_cut5_reproduce_stable2024old.csv',skiprows=range(4000,5000), nrows=1000)
#
# df = pd.read_csv('modal_analysis data_stable2024old_checknewpfd.csv',skiprows=range(4000,5000), nrows=1000)
# df = pd.read_csv('modal_analysis data_stable2024old_2.csv',skiprows=range(4000,5000), nrows=1000)
df = pd.read_csv('modal_analysis data_stable2024old_checknewpfd(2).csv',skiprows=range(4000,5000), nrows=1000)
# df = pd.read_csv('modal_analysis data_stable2024_noload.csv',skiprows=range(4000,5000), nrows=1000)
df = pd.read_csv('modal_analysis data_stable2024_20001.csv',skiprows=range(1,999), nrows=1000)

df = pd.read_csv('modal_analysis data_stable2024_20001.csv',skiprows=range(4000,5000), nrows=200)

df = pd.read_csv('modal_analysis data_stable2024_20001.csv',skiprows=range(4000,5000), nrows=200)

df = pd.read_csv('modal_analysis data_stable2024_20001.csv',skiprows=range(1,900), nrows=1000)




# modal_analysis data_cut5_working_2025-08-09_10-57-04
feature_columns = df.columns[1:40]
data = df[feature_columns].values.T

# 数据预处理
data_detrended = data - np.mean(data, axis=1, keepdims=True)
data_norm = (data_detrended - data_detrended.min(axis=1, keepdims=True)) / \
            (data_detrended.max(axis=1, keepdims=True) - data_detrended.min(axis=1, keepdims=True))

# 获取真实特征值
true_eigs = []
for i in range(10):
    real_col = f'EV_{i}_real'
    imag_col = f'EV_{i}_imag'
    if real_col in df.columns and imag_col in df.columns:
        true_eigs.append(complex(df[real_col].iloc[0], df[imag_col].iloc[0]))
true_eigs = np.array(true_eigs)

# 创建时间向量
time = df['Time'].values[:data_norm.shape[1]]
dt = time[1] - time[0]

# 定义要比较的DMD方法
methods = {
    'Standard DMD': DMD(svd_rank=10),
    'Forward-Backward DMD': FbDMD(svd_rank=10),
    'Hankel DMD': HankelDMD(svd_rank=10),
    'Higher-Order DMD': HODMD(svd_rank=20, d=10, opt=True),
    'Compressed DMD': CDMD(svd_rank=10),
    'Optimized DMD': OptDMD(svd_rank=10),
    'Robust DMD': RDMD(svd_rank=10),
    'Balanced Optimal DMD': BOPDMD(svd_rank=10),
}

# 为每个方法单独创建图形
for name, method in methods.items():
    plt.figure(figsize=(8, 8))
    plt.title(f"{name} vs True Eigenvalues")
    unit_circle = plt.Circle((0, 0), 1, color='black', fill=False, linestyle='--')
    plt.gca().add_artist(unit_circle)

    try:
        # 拟合方法(BOPDMD需要特殊处理)
        if name == 'Balanced Optimal DMD':
            method.fit(data_norm, t=time)
        else:
            method.fit(data_norm)

        # 获取连续时间特征值
        eigs_continuous = np.log(method.eigs) / dt
        mask = eigs_continuous.real > -4  # 过滤条件
        # eigs_continuous = eigs_continuous[mask]
         # 绘制真实特征值(黑色x)
        plt.scatter(np.real(true_eigs), np.imag(true_eigs), color='black',
                    marker='x', s=100, label='True Eigenvalues', linewidth=2)

        # 绘制DMD特征值(红色圆圈)
        plt.scatter(eigs_continuous[mask].real, eigs_continuous[mask].imag,
                    color='red', label=name, alpha=0.7)

        # 添加标注
        for i, eig in enumerate(eigs_continuous[mask]):
            plt.annotate(f'{i}', (eig.real, eig.imag))

        plt.xlabel('Real')
        plt.ylabel('Imaginary')
        plt.grid(True)
        plt.axis('equal')
        plt.legend()
        plt.tight_layout()

    except Exception as e:
        print(f"处理 {name} 时出错: {str(e)}")
        plt.close()
        continue
    if name=="Higher-Order DMD":
        ## print out the eigenvalue
        print(f"Higher-Order DMD Eigenvalues:\n  {eigs_continuous[mask]}")
    # 显示图形
plt.show()